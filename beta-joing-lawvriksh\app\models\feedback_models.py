from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from enum import Enum

class FollowUpConsent(str, Enum):
    YES = "yes"
    NO = "no"

class FeedbackData(BaseModel):
    user_email: Optional[EmailStr] = None

    # Digital Presence
    digital_activity_level: Optional[int] = None  # 1-5 scale
    digital_showcase_effectiveness: Optional[int] = None  # 1-5 scale
    digital_sharing_difficulty: Optional[int] = None  # 1-5 scale

    # Blogging and AI Usage
    regular_blogging: Optional[str] = None  # Yes/No
    ai_tools_frequency: Optional[str] = None  # Never/Rarely/Sometimes/Often/Always
    blogging_tools_familiarity: Optional[int] = None  # 1-5 scale

    # Blogging Platform Expectations
    core_features_expectation: Optional[str] = None  # Text area
    ai_research_opinion: Optional[str] = None  # Text area
    ideal_reading_features: Optional[str] = None  # Text area
    portfolio_presentation_preference: Optional[str] = None  # Text area

    # Follow-up
    follow_up_consent: Optional[FollowUpConsent] = FollowUpConsent.NO
    follow_up_email: Optional[EmailStr] = None

    @validator('digital_activity_level', 'digital_showcase_effectiveness', 'digital_sharing_difficulty', 'blogging_tools_familiarity')
    def validate_rating(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Rating must be between 1 and 5')
        return v
    
    @validator('follow_up_email')
    def validate_follow_up_email(cls, v, values):
        if values.get('follow_up_consent') == FollowUpConsent.YES and not v:
            raise ValueError('Follow-up email is required when consent is given')
        return v 