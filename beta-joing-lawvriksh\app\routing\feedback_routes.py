from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.models.feedback_models import FeedbackData
from app.services.feedback_service import FeedbackService
from app.services.auth_service import AuthService
from app.schemas.response_schemas import BaseResponse
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/feedback", tags=["Feedback"])
security = HTTPBearer()

def verify_admin_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """Verify admin JWT token"""
    try:
        payload = AuthService.verify_token(credentials.credentials)
        if not payload or payload.get("role") != "admin":
            raise HTTPException(status_code=401, detail="Invalid or expired token")
        return payload
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(status_code=401, detail="Invalid token")

@router.post("/submit", response_model=BaseResponse)
async def submit_feedback(feedback_data: FeedbackData):
    """
    Submit feedback data
    
    - **user_email**: User's email (optional)
    - **digital_activity_level**: Digital activity level in profession (1-5, optional)
    - **digital_showcase_effectiveness**: Effectiveness of showcasing digital work (1-5, optional)
    - **digital_sharing_difficulty**: Difficulty level in sharing digital work (1-5, optional)
    - **regular_blogging**: Whether user writes blogs regularly (Yes/No, optional)
    - **ai_tools_frequency**: Frequency of AI tools usage for blogging (Never/Rarely/Sometimes/Often/Always, optional)
    - **blogging_tools_familiarity**: Familiarity with blogging tools (1-5, optional)
    - **core_features_expectation**: Expected core features from blogging platform (optional)
    - **ai_research_opinion**: Opinion on using AI for research during blog creation (optional)
    - **ideal_reading_features**: Ideal features for blog reading platform (optional)
    - **portfolio_presentation_preference**: Preferred portfolio presentation style (optional)
    - **follow_up_consent**: Consent for follow-up (yes/no, optional)
    - **follow_up_email**: Email for follow-up (required if consent is yes)
    """
    try:
        result = FeedbackService.save_feedback(feedback_data)
        return result
    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        return BaseResponse(
            success=False,
            message="Failed to submit feedback"
        )

@router.get("/all", response_model=BaseResponse)
async def get_all_feedback(payload: dict = Depends(verify_admin_token)):
    """
    Get all feedback data (Admin only)
    """
    try:
        feedback = FeedbackService.get_all_feedback()
        return BaseResponse(
            success=True,
            message="Feedback retrieved successfully",
            data=feedback
        )
    except Exception as e:
        logger.error(f"Error fetching feedback: {e}")
        return BaseResponse(
            success=False,
            message="Failed to fetch feedback"
        )

@router.get("/userfeedbackdata", response_model=BaseResponse)
async def get_user_feedback_data(payload: dict = Depends(verify_admin_token)):
    """
    Get all feedback data (Admin only, alternate endpoint)
    """
    try:
        feedback = FeedbackService.get_all_feedback()
        return BaseResponse(
            success=True,
            message="Feedback retrieved successfully",
            data=feedback
        )
    except Exception as e:
        logger.error(f"Error fetching feedback: {e}")
        return BaseResponse(
            success=False,
            message="Failed to fetch feedback"
        )

@router.get("/analytics", response_model=BaseResponse)
async def get_feedback_analytics(payload: dict = Depends(verify_admin_token)):
    """
    Get feedback analytics and statistics (Admin only)
    """
    try:
        analytics = FeedbackService.get_feedback_analytics()
        return BaseResponse(
            success=True,
            message="Analytics retrieved successfully",
            data=analytics
        )
    except Exception as e:
        logger.error(f"Error fetching feedback analytics: {e}")
        return BaseResponse(
            success=False,
            message="Failed to fetch analytics"
        )

@router.get("/summary", response_model=BaseResponse)
async def get_feedback_summary(payload: dict = Depends(verify_admin_token)):
    """
    Get feedback summary (Admin only)
    """
    try:
        summary = FeedbackService.get_feedback_summary()
        return BaseResponse(
            success=True,
            message="Summary retrieved successfully",
            data=summary
        )
    except Exception as e:
        logger.error(f"Error fetching feedback summary: {e}")
        return BaseResponse(
            success=False,
            message="Failed to fetch summary"
        ) 