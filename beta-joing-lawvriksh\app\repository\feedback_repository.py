# Import database (SQLite for development, MySQL for production)
try:
    from database import db
except ImportError:
    from database_sqlite import db
from app.models.feedback_models import FeedbackData
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

class FeedbackRepository:
    
    @staticmethod
    def save_feedback(feedback_data: FeedbackData) -> bool:
        """Save feedback data to database"""
        try:
            # Start transaction (SQLite doesn't need explicit start)
            if hasattr(db.connection, 'start_transaction'):
                db.connection.start_transaction()
            
            # Insert into feedback_forms
            feedback_query = "INSERT INTO feedback_forms (user_email) VALUES (%s)"
            feedback_result = db.execute_query(feedback_query, (feedback_data.user_email,))
            
            if feedback_result <= 0:
                if hasattr(db.connection, 'rollback'):
                    db.connection.rollback()
                return False
            
            feedback_form_id = db.cursor.lastrowid
            
            # Insert digital presence ratings if provided
            if any([
                feedback_data.digital_activity_level,
                feedback_data.digital_showcase_effectiveness,
                feedback_data.digital_sharing_difficulty
            ]):
                digital_query = """
                    INSERT INTO digital_presence (
                        feedback_form_id, digital_activity_level, digital_showcase_effectiveness,
                        digital_sharing_difficulty
                    ) VALUES (%s, %s, %s, %s)
                """
                digital_params = (
                    feedback_form_id,
                    feedback_data.digital_activity_level,
                    feedback_data.digital_showcase_effectiveness,
                    feedback_data.digital_sharing_difficulty
                )
                db.execute_query(digital_query, digital_params)

            # Insert blogging and AI usage data if provided
            if any([
                feedback_data.regular_blogging,
                feedback_data.ai_tools_frequency,
                feedback_data.blogging_tools_familiarity
            ]):
                blogging_query = """
                    INSERT INTO blogging_ai_usage (
                        feedback_form_id, regular_blogging, ai_tools_frequency,
                        blogging_tools_familiarity
                    ) VALUES (%s, %s, %s, %s)
                """
                blogging_params = (
                    feedback_form_id,
                    feedback_data.regular_blogging,
                    feedback_data.ai_tools_frequency,
                    feedback_data.blogging_tools_familiarity
                )
                db.execute_query(blogging_query, blogging_params)
            
            # Insert blogging platform expectations if provided
            if any([
                feedback_data.core_features_expectation,
                feedback_data.ai_research_opinion,
                feedback_data.ideal_reading_features,
                feedback_data.portfolio_presentation_preference,
                feedback_data.follow_up_consent,
                feedback_data.follow_up_email
            ]):
                expectations_query = """
                    INSERT INTO blogging_platform_expectations (
                        feedback_form_id, core_features_expectation, ai_research_opinion,
                        ideal_reading_features, portfolio_presentation_preference,
                        follow_up_consent, follow_up_email
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                expectations_params = (
                    feedback_form_id,
                    feedback_data.core_features_expectation,
                    feedback_data.ai_research_opinion,
                    feedback_data.ideal_reading_features,
                    feedback_data.portfolio_presentation_preference,
                    feedback_data.follow_up_consent,
                    feedback_data.follow_up_email
                )
                db.execute_query(expectations_query, expectations_params)
            
            # Commit transaction
            if hasattr(db.connection, 'commit'):
                db.connection.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving feedback: {e}")
            if hasattr(db.connection, 'rollback'):
                db.connection.rollback()
            return False
    
    @staticmethod
    def get_all_feedback() -> List[dict]:
        """Get all feedback data with related information"""
        try:
            query = """
                SELECT
                    f.id, f.user_email, f.created_at,
                    dp.digital_activity_level, dp.digital_showcase_effectiveness, dp.digital_sharing_difficulty,
                    ba.regular_blogging, ba.ai_tools_frequency, ba.blogging_tools_familiarity,
                    bpe.core_features_expectation, bpe.ai_research_opinion,
                    bpe.ideal_reading_features, bpe.portfolio_presentation_preference,
                    bpe.follow_up_consent, bpe.follow_up_email
                FROM feedback_forms f
                LEFT JOIN digital_presence dp ON f.id = dp.feedback_form_id
                LEFT JOIN blogging_ai_usage ba ON f.id = ba.feedback_form_id
                LEFT JOIN blogging_platform_expectations bpe ON f.id = bpe.feedback_form_id
                ORDER BY f.created_at DESC
            """
            return db.execute_query(query)
        except Exception as e:
            logger.error(f"Error fetching feedback: {e}")
            return []
    
    @staticmethod
    def get_feedback_analytics() -> dict:
        """Get feedback analytics and statistics"""
        try:
            # Get average ratings
            avg_ratings_query = """
                SELECT
                    AVG(dp.digital_activity_level) as avg_digital_activity,
                    AVG(dp.digital_showcase_effectiveness) as avg_showcase_effectiveness,
                    AVG(dp.digital_sharing_difficulty) as avg_sharing_difficulty,
                    AVG(ba.blogging_tools_familiarity) as avg_blogging_familiarity
                FROM feedback_forms f
                LEFT JOIN digital_presence dp ON f.id = dp.feedback_form_id
                LEFT JOIN blogging_ai_usage ba ON f.id = ba.feedback_form_id
            """
            avg_ratings = db.execute_query(avg_ratings_query)
            
            # Get total feedback count
            count_query = "SELECT COUNT(*) as total_feedback FROM feedback_forms"
            total_count = db.execute_query(count_query)
            
            # Get follow-up consent count
            consent_query = """
                SELECT 
                    follow_up_consent,
                    COUNT(*) as count
                FROM suggestions_and_needs 
                WHERE follow_up_consent IS NOT NULL
                GROUP BY follow_up_consent
            """
            consent_stats = db.execute_query(consent_query)
            
            return {
                'average_ratings': avg_ratings[0] if avg_ratings else {},
                'total_feedback': total_count[0]['total_feedback'] if total_count else 0,
                'consent_stats': consent_stats
            }
        except Exception as e:
            logger.error(f"Error fetching feedback analytics: {e}")
            return {} 