-- =====================================================
-- LAWVIKSH JOINING LIST DATABASE SCHEMA (UPDATED)
-- MySQL Workbench compatible
-- =====================================================

-- Create database
CREATE DATABASE IF NOT EXISTS lawviksh_db;
USE lawviksh_db;

-- =====================================================
-- 1. USERS TABLE (for "Join as USER" and "Join as CREATOR" forms)
-- =====================================================

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    phone_number VARCHAR(20) NOT NULL,
    gender ENUM('Male', 'Female', 'Other', 'Prefer not to say') NULL,
    profession ENUM('Student', 'Lawyer', 'Other') NULL,
    interest_reason TEXT NULL,
    user_type ENUM('user', 'creator') NOT NULL DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
);
GRANT ALL PRIVILEGES ON lawviksh_db.* TO 'root'@'localhost';
FLUSH PRIVILEGES;

-- =====================================================
-- 2. NOT INTERESTED TABLE (for "Not Interested" form)
-- =====================================================

CREATE TABLE not_interested_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    gender ENUM('Male', 'Female', 'Other', 'Prefer not to say') NULL,
    profession ENUM('Student', 'Lawyer', 'Other') NULL,
    not_interested_reason ENUM('Too complex', 'Not relevant', 'Other') NULL,
    improvement_suggestions TEXT NULL,
    interest_reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_reason (not_interested_reason),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 3. FEEDBACK FORMS TABLE (for all feedback data)
-- =====================================================

CREATE TABLE feedback_forms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_email VARCHAR(255) NULL, -- Optional, for follow-up consent
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_email (user_email),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 4. DIGITAL PRESENCE TABLE
-- =====================================================

CREATE TABLE digital_presence (
    id INT AUTO_INCREMENT PRIMARY KEY,
    feedback_form_id INT NOT NULL,
    digital_activity_level INT CHECK (digital_activity_level BETWEEN 1 AND 5),
    digital_showcase_effectiveness INT CHECK (digital_showcase_effectiveness BETWEEN 1 AND 5),
    digital_sharing_difficulty INT CHECK (digital_sharing_difficulty BETWEEN 1 AND 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (feedback_form_id) REFERENCES feedback_forms(id) ON DELETE CASCADE,
    INDEX idx_feedback_form (feedback_form_id),
    INDEX idx_ratings (digital_activity_level, digital_showcase_effectiveness, digital_sharing_difficulty)
);

-- =====================================================
-- 5. BLOGGING AND AI USAGE TABLE
-- =====================================================

CREATE TABLE blogging_ai_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    feedback_form_id INT NOT NULL,
    regular_blogging ENUM('Yes', 'No') NULL,
    ai_tools_frequency ENUM('Never', 'Rarely', 'Sometimes', 'Often', 'Always') NULL,
    blogging_tools_familiarity INT CHECK (blogging_tools_familiarity BETWEEN 1 AND 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (feedback_form_id) REFERENCES feedback_forms(id) ON DELETE CASCADE,
    INDEX idx_feedback_form (feedback_form_id),
    INDEX idx_blogging_frequency (ai_tools_frequency)
);

-- =====================================================
-- 6. BLOGGING PLATFORM EXPECTATIONS TABLE
-- =====================================================

CREATE TABLE blogging_platform_expectations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    feedback_form_id INT NOT NULL,
    core_features_expectation TEXT NULL,
    ai_research_opinion TEXT NULL,
    ideal_reading_features TEXT NULL,
    portfolio_presentation_preference TEXT NULL,
    follow_up_consent ENUM('yes', 'no') DEFAULT 'no',
    follow_up_email VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (feedback_form_id) REFERENCES feedback_forms(id) ON DELETE CASCADE,
    INDEX idx_feedback_form (feedback_form_id),
    INDEX idx_follow_up_consent (follow_up_consent)
);

-- =====================================================
-- 7. FORM SUBMISSIONS LOG TABLE (for tracking all submissions)
-- =====================================================

CREATE TABLE form_submissions_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_type ENUM('join_as_user', 'not_interested', 'feedback') NOT NULL,
    user_ip VARCHAR(45) NULL, -- IPv6 compatible
    user_agent TEXT NULL,
    submission_data JSON NULL, -- Store complete JSON for backup/audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_form_type (form_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_ip (user_ip)
);

-- =====================================================
-- 8. ANALYTICS VIEWS (for easy reporting)
-- =====================================================

-- View for user/creator registration analytics
CREATE OR REPLACE VIEW user_registration_analytics AS
SELECT 
    DATE(created_at) as registration_date,
    COUNT(*) as total_registrations,
    COUNT(CASE WHEN user_type = 'user' THEN 1 END) as user_count,
    COUNT(CASE WHEN user_type = 'creator' THEN 1 END) as creator_count,
    COUNT(CASE WHEN gender = 'Male' THEN 1 END) as male_count,
    COUNT(CASE WHEN gender = 'Female' THEN 1 END) as female_count,
    COUNT(CASE WHEN gender = 'Other' THEN 1 END) as other_count,
    COUNT(CASE WHEN gender = 'Prefer not to say' THEN 1 END) as prefer_not_to_say_count,
    COUNT(CASE WHEN profession = 'Student' THEN 1 END) as student_count,
    COUNT(CASE WHEN profession = 'Lawyer' THEN 1 END) as lawyer_count,
    COUNT(CASE WHEN profession = 'Other' THEN 1 END) as other_profession_count
FROM users 
GROUP BY DATE(created_at)
ORDER BY registration_date DESC;

-- View for feedback analytics
CREATE OR REPLACE VIEW feedback_analytics AS
SELECT
    f.id as feedback_id,
    f.user_email,
    f.created_at,
    -- Digital Presence Ratings
    dp.digital_activity_level,
    dp.digital_showcase_effectiveness,
    dp.digital_sharing_difficulty,
    -- Blogging and AI Usage
    ba.regular_blogging,
    ba.ai_tools_frequency,
    ba.blogging_tools_familiarity,
    -- Platform Expectations
    bpe.core_features_expectation,
    bpe.ai_research_opinion,
    bpe.ideal_reading_features,
    bpe.portfolio_presentation_preference,
    -- Average ratings
    ROUND((dp.digital_activity_level + dp.digital_showcase_effectiveness + (6 - dp.digital_sharing_difficulty) + ba.blogging_tools_familiarity) / 4, 2) as avg_overall_rating,
    -- Follow-up consent
    bpe.follow_up_consent
FROM feedback_forms f
LEFT JOIN digital_presence dp ON f.id = dp.feedback_form_id
LEFT JOIN blogging_ai_usage ba ON f.id = ba.feedback_form_id
LEFT JOIN blogging_platform_expectations bpe ON f.id = bpe.feedback_form_id
ORDER BY f.created_at DESC;

-- =====================================================
-- 9. SAMPLE DATA INSERTION (for testing)
-- =====================================================

-- Sample user registrations
INSERT INTO users (name, email, phone_number, gender, profession, interest_reason, user_type) VALUES
('John Doe', '<EMAIL>', '+1234567890', 'Male', 'Student', 'Interested in learning about legal processes', 'user'),
('Jane Smith', '<EMAIL>', '+1234567891', 'Female', 'Lawyer', 'Looking for legal resources', 'creator'),
('Alex Johnson', '<EMAIL>', '+1234567892', 'Other', 'Other', 'General interest in law', 'user');

-- Sample not interested users
INSERT INTO not_interested_users (name, email, phone_number, gender, profession, not_interested_reason, improvement_suggestions, interest_reason) VALUES
('Bob Wilson', '<EMAIL>', '+1234567893', 'Male', 'Other', 'Too complex', 'Please simplify the interface', 'Not interested in legal resources'),
('Sarah Brown', '<EMAIL>', '+1234567894', 'Female', 'Lawyer', 'Not relevant', 'Not applicable to my needs', 'No interest');

-- Sample feedback form
INSERT INTO feedback_forms (user_email) VALUES
('<EMAIL>');

-- Sample digital presence data
INSERT INTO digital_presence (feedback_form_id, digital_activity_level, digital_showcase_effectiveness, digital_sharing_difficulty) VALUES
(1, 4, 3, 2);

-- Sample blogging and AI usage data
INSERT INTO blogging_ai_usage (feedback_form_id, regular_blogging, ai_tools_frequency, blogging_tools_familiarity) VALUES
(1, 'Yes', 'Sometimes', 4);

-- Sample blogging platform expectations
INSERT INTO blogging_platform_expectations (feedback_form_id, core_features_expectation, ai_research_opinion, ideal_reading_features, portfolio_presentation_preference, follow_up_consent, follow_up_email) VALUES
(1, 'Easy-to-use editor, SEO optimization, analytics dashboard', 'AI can be helpful for research but human insight is crucial for quality content', 'Listening mode, bookmarking, offline reading, dark mode', 'Clean layout with project showcases, testimonials, and downloadable portfolio', 'yes', '<EMAIL>');

-- =====================================================
-- 10. USEFUL QUERIES FOR ANALYSIS
-- =====================================================

-- Query to get total registrations by month
-- SELECT 
--     DATE_FORMAT(created_at, '%Y-%m') as month,
--     COUNT(*) as registrations
-- FROM users 
-- GROUP BY DATE_FORMAT(created_at, '%Y-%m')
-- ORDER BY month DESC;

-- Query to get average feedback ratings
-- SELECT 
--     AVG(visual_design_rating) as avg_visual_design,
--     AVG(ease_of_navigation_rating) as avg_navigation,
--     AVG(mobile_responsiveness_rating) as avg_mobile,
--     AVG(overall_satisfaction_rating) as avg_satisfaction,
--     AVG(task_completion_rating) as avg_task_completion,
--     AVG(service_quality_rating) as avg_service_quality
-- FROM ui_ratings ui
-- JOIN ux_ratings ux ON ui.feedback_form_id = ux.feedback_form_id;

-- Query to get most common improvement suggestions
-- SELECT 
--     improvement_suggestions,
--     COUNT(*) as frequency
-- FROM suggestions_and_needs 
-- WHERE improvement_suggestions IS NOT NULL 
-- GROUP BY improvement_suggestions 
-- ORDER BY frequency DESC 
-- LIMIT 10;

-- =====================================================
-- END OF SCHEMA
-- =====================================================
