import { POPUP_STYLES } from "~/lib/utils";
import { useState } from "react";
import type { FeedbackFormData, FollowUpConsent } from "~/lib/types";
import {
  RATING_SCALE,
  FOLLOW_UP_CONSENT_OPTIONS,
  BL<PERSON><PERSON><PERSON>NG_OPTIONS,
  AI_TOOLS_FREQUENCY_OPTIONS,
  DIGITAL_ACTIVITY_LABELS,
  SHOWCASE_EFFECTIVENESS_LABELS,
  DIFFICULTY_LABELS,
  FAMILIARITY_LABELS
} from "~/lib/types";
import { useFeedbackSubmission } from "~/lib/hooks";

interface FeedbackFormProps {
  onClose: () => void;
  onSuccess?: () => void;
}

export function FeedbackForm({ onClose, onSuccess }: FeedbackFormProps) {
  const [formData, setFormData] = useState<FeedbackFormData>({
    user_email: '',
    digital_activity_level: undefined,
    digital_showcase_effectiveness: undefined,
    digital_sharing_difficulty: undefined,
    regular_blogging: '',
    ai_tools_frequency: '',
    blogging_tools_familiarity: undefined,
    core_features_expectation: '',
    ai_research_opinion: '',
    ideal_reading_features: '',
    portfolio_presentation_preference: '',
    follow_up_consent: 'no',
    follow_up_email: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { submit, isSubmitting, error, success, successMessage, reset } = useFeedbackSubmission();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    let processedValue: any = value;

    // Handle radio buttons for ratings
    if (type === 'radio' && name.includes('_rating')) {
      processedValue = parseInt(value);
    }

    // Handle empty strings for optional fields
    if (value === '' && !name.includes('_rating')) {
      processedValue = undefined;
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.user_email?.trim()) {
      newErrors.user_email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.user_email)) {
      newErrors.user_email = 'Please enter a valid email address';
    }

    // Validate follow-up email if consent is given
    if (formData.follow_up_consent === 'yes' && !formData.follow_up_email?.trim()) {
      newErrors.follow_up_email = 'Follow-up email is required when consent is given';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const success = await submit(formData);
    if (success) {
      onSuccess?.();
      // Keep form open to show success message
    }
  };

  // Handle closing after success
  const handleClose = () => {
    reset();
    onClose();
  };

  const renderRatingQuestion = (
    name: string,
    label: string,
    questionNumber: number,
    ratingLabels: Record<number, string>
  ) => (
    <div className="mb-6">
      <label className="block font-medium text-sm text-gray-800 mb-3">
        {questionNumber}. {label}
      </label>
      <div className="flex gap-4 flex-wrap mb-3">
        {RATING_SCALE.map(num => (
          <label key={num} className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
            <input
              type="radio"
              name={name}
              value={num.toString()}
              checked={formData[name as keyof FeedbackFormData] === num}
              onChange={handleInputChange}
              className="w-4 h-4 accent-law-gold"
            />
            <span className="font-medium">{num} - {ratingLabels[num]}</span>
          </label>
        ))}
      </div>
    </div>
  );

  const renderRadioQuestion = (
    name: string,
    label: string,
    questionNumber: number,
    options: Array<{ value: string; label: string }>
  ) => (
    <div className="mb-6">
      <label className="block font-medium text-sm text-gray-800 mb-3">
        {questionNumber}. {label}
      </label>
      <div className="flex flex-col gap-3">
        {options.map(option => (
          <label key={option.value} className="flex items-center gap-2 cursor-pointer text-sm text-gray-600">
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={formData[name as keyof FeedbackFormData] === option.value}
              onChange={handleInputChange}
              className="w-4 h-4 accent-law-gold"
            />
            <span>{option.label}</span>
          </label>
        ))}
      </div>
    </div>
  );

  const renderTextAreaQuestion = (
    name: string,
    label: string,
    questionNumber: number,
    placeholder: string
  ) => (
    <div className="mb-6">
      <label htmlFor={name} className="block font-medium text-sm text-gray-800 mb-3">
        {questionNumber}. {label}
      </label>
      <textarea
        id={name}
        name={name}
        value={formData[name as keyof FeedbackFormData] as string || ''}
        onChange={handleInputChange}
        rows={4}
        className={POPUP_STYLES.textarea}
        placeholder={placeholder}
      />
    </div>
  );

  return (
    <div className="w-full">
      <h2 className={POPUP_STYLES.title}>
        We value your insights and would love to stay connected
      </h2>

      <p className="text-center text-gray-600 text-sm mb-3">
        <em>Estimated time: 5-7 minutes</em>
      </p>

      <p className="text-sm text-gray-600 mb-8 text-center leading-relaxed">
        Are you open to being contacted further? Your insights help us understand your digital presence, blogging habits, and expectations from our platform.
      </p>

      <form onSubmit={handleSubmit} className="flex flex-col gap-8">
        {/* Email Field */}
        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="user_email" className={POPUP_STYLES.label}>
            Email Address *
          </label>
          <input
            type="email"
            id="user_email"
            name="user_email"
            value={formData.user_email || ''}
            onChange={handleInputChange}
            className={`${POPUP_STYLES.input} ${errors.user_email ? 'border-red-500' : ''}`}
            placeholder="Enter your email address"
            required
          />
          {errors.user_email && (
            <p className="text-red-500 text-sm mt-1">{errors.user_email}</p>
          )}
        </div>

        {/* Digital Presence Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-3 bg-cover bg-center">
            Digital Presence
          </h3>

          {renderRatingQuestion('digital_activity_level', 'How active are you digitally in relation to your profession?', 1, DIGITAL_ACTIVITY_LABELS)}
          {renderRatingQuestion('digital_showcase_effectiveness', 'How effectively are you able to showcase your digital work online?', 2, SHOWCASE_EFFECTIVENESS_LABELS)}
          {renderRatingQuestion('digital_sharing_difficulty', 'What level of difficulty do you face while sharing your digital work online?', 3, DIFFICULTY_LABELS)}
        </div>

        {/* Blogging and Sharing Insights Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-3 bg-cover bg-center">
            Blogging and Sharing Insights
          </h3>

          {renderRadioQuestion('regular_blogging', 'Do you write blogs regularly?', 4, BLOGGING_OPTIONS)}
          {renderRadioQuestion('ai_tools_frequency', 'How often do you use AI tools to assist in writing blogs?', 5, AI_TOOLS_FREQUENCY_OPTIONS)}
          {renderRatingQuestion('blogging_tools_familiarity', 'How familiar are you with different blogging tools available in the market?', 6, FAMILIARITY_LABELS)}
        </div>

        {/* Expectations from a Blogging Platform Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-5 bg-cover bg-center">
            Expectations from a Blogging Platform
          </h3>

          {renderTextAreaQuestion(
            'core_features_expectation',
            'What core features do you expect from a blogging platform, either as a creator or as a reader?',
            7,
            'Describe the essential features you would expect...'
          )}

          {renderTextAreaQuestion(
            'ai_research_opinion',
            'What is your opinion on using AI for research during blog creation?',
            8,
            'Share your thoughts on AI-assisted research...'
          )}

          {renderTextAreaQuestion(
            'ideal_reading_features',
            'What features would your ideal blog reading platform include (e.g., listening mode, etc.)?',
            9,
            'Describe features that would enhance your reading experience...'
          )}

          {renderTextAreaQuestion(
            'portfolio_presentation_preference',
            'How would you prefer to present your portfolio, and what type of content do you want to include to attract recruiters or relevant audiences?',
            10,
            'Describe your ideal portfolio presentation and content strategy...'
          )}
        </div>
        {/* Follow-up Section */}
        <div>
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-xl mb-5 bg-cover bg-center">
            Follow-up Consent
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label className="block font-medium text-base text-gray-800 mb-3">
              11. Do you consent to follow-up contact regarding your feedback?
            </label>
            <div className="flex flex-col gap-3">
              {FOLLOW_UP_CONSENT_OPTIONS.map(option => (
                <label key={option.value} className="flex items-center gap-2 cursor-pointer text-sm text-gray-600">
                  <input
                    type="radio"
                    name="follow_up_consent"
                    value={option.value}
                    checked={formData.follow_up_consent === option.value}
                    onChange={handleInputChange}
                    className="w-4 h-4 accent-law-gold"
                  />
                  <span>{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          {formData.follow_up_consent === 'yes' && (
            <div className={POPUP_STYLES.formGroup}>
              <label htmlFor="follow_up_email" className={POPUP_STYLES.label}>
                Follow-up Email Address *
              </label>
              <input
                type="email"
                id="follow_up_email"
                name="follow_up_email"
                value={formData.follow_up_email || ''}
                onChange={handleInputChange}
                className={`${POPUP_STYLES.input} ${errors.follow_up_email ? 'border-red-500' : ''}`}
                placeholder="Enter email for follow-up contact"
                required={formData.follow_up_consent === 'yes'}
              />
              {errors.follow_up_email && (
                <p className="text-red-500 text-sm mt-1">{errors.follow_up_email}</p>
              )}
            </div>
          )}
        </div>

        {/* Success Message */}
        {success && successMessage && (
          <div className="bg-green-50 text-green-800 p-4 rounded-lg border border-green-200 text-center">
            <p className="font-medium">{successMessage}</p>
            <button
              type="button"
              onClick={handleClose}
              className="mt-2 text-sm text-green-600 hover:text-green-800 underline"
            >
              Close
            </button>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 text-red-800 p-4 rounded-lg border border-red-200 text-center">
            <p>{error}</p>
          </div>
        )}

        {!success && (
          <button
            type="submit"
            className={`${POPUP_STYLES.button} ${isSubmitting ? 'opacity-60 cursor-not-allowed' : ''}`}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
          </button>
        )}
      </form>
    </div>
  );
}

export default FeedbackForm;
